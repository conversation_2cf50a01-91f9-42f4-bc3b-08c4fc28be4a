// Test script to verify multipart upload functionality
// This script can be run in the browser console to test the fix

async function testMultipartUpload() {
  console.log('Testing multipart upload functionality...');
  
  // Create a test blob (simulating a file chunk)
  const testData = new Uint8Array(1024 * 1024); // 1MB test data
  for (let i = 0; i < testData.length; i++) {
    testData[i] = i % 256;
  }
  const testBlob = new Blob([testData], { type: 'application/octet-stream' });
  
  console.log('Created test blob:', testBlob.size, 'bytes');
  
  // Test the axios configuration we're using
  const axios = window.axios || (await import('axios')).default;
  
  // Create the same axios instance as in our fix
  const s3AxiosInstance = axios.create({
    transformRequest: [(data) => data], // Pass data as-is without transformation
  });
  
  console.log('Created S3 axios instance');
  
  // Test URL (this would normally be a real presigned URL from S3)
  const testUrl = 'https://httpbin.org/put'; // Using httpbin for testing
  
  try {
    console.log('Testing PUT request with blob...');
    
    const response = await s3AxiosInstance.put(testUrl, testBlob, {
      onUploadProgress: (progressEvent) => {
        if (progressEvent.total) {
          const progress = progressEvent.loaded;
          console.log(`Upload progress: ${progress} bytes`);
        }
      },
    });
    
    console.log('Response status:', response.status);
    console.log('Response headers:', response.headers);
    console.log('Request headers that were sent:', response.config.headers);
    
    // Check if Content-Type was automatically set
    const sentHeaders = response.config.headers;
    if (sentHeaders['Content-Type'] || sentHeaders['content-type']) {
      console.warn('⚠️  Content-Type header was set:', sentHeaders['Content-Type'] || sentHeaders['content-type']);
      console.warn('This might cause SignatureDoesNotMatch errors with S3');
    } else {
      console.log('✅ No Content-Type header was set - this is correct for S3 multipart parts');
    }
    
  } catch (error) {
    console.error('Test failed:', error);
    if (error.response) {
      console.error('Response data:', error.response.data);
      console.error('Response headers:', error.response.headers);
    }
  }
}

// Export for use in browser console
if (typeof window !== 'undefined') {
  window.testMultipartUpload = testMultipartUpload;
  console.log('Test function available as window.testMultipartUpload()');
}

// Auto-run if in Node.js environment
if (typeof module !== 'undefined' && module.exports) {
  module.exports = testMultipartUpload;
}
