# Multipart Upload SignatureDoesNotMatch Fix

## Problem Description

The multipart file upload functionality was failing with AWS S3 "SignatureDoesNotMatch" errors during the upload of individual parts to S3 using presigned URLs.

### Root Cause

The issue occurred because:

1. **Backend**: The presigned URLs for multipart upload parts are generated using `s3.generate_presigned_url('upload_part', ...)` without including Content-Type in the signature (this is correct behavior for S3 multipart parts)

2. **Frontend**: Axi<PERSON> was automatically setting a `Content-Type` header (often `application/x-www-form-urlencoded`) when uploading Blob data, but this header wasn't included in the presigned URL signature

3. **S3 Validation**: When S3 received the request with a Content-Type header that wasn't part of the original signature, it rejected the request with SignatureDoesNotMatch

## Solution Implemented

### Frontend Changes (`src/services/multipartUpload.service.ts`)

1. **Created Custom Axios Instance**: Instead of using the default axios instance (which has default headers), we create a clean axios instance specifically for S3 uploads:

```typescript
const s3AxiosInstance = axios.create({
  // Don't set any default headers that could interfere with S3 signature
  transformRequest: [(data) => data], // Pass data as-is without transformation
})
```

2. **Removed Content-Type Header**: The PUT request to S3 no longer includes any Content-Type header, matching what was used to generate the presigned URL signature.

3. **Enhanced Error Handling**: The existing error handling already detects SignatureDoesNotMatch errors and provides appropriate retry logic.

### Backend Changes (`storage/multipart_upload.py`)

1. **Updated Comments**: Clarified that Content-Type is intentionally NOT included in multipart part signatures, which is the correct AWS S3 behavior.

## Technical Details

### Why This Fix Works

1. **S3 Multipart Upload Design**: For multipart uploads, each part can theoretically have different content types, so S3 doesn't require Content-Type to be part of the signature for individual parts.

2. **Presigned URL Signature**: The signature only includes the parameters that were specified when generating the URL. Since we don't specify Content-Type, it shouldn't be included in the request.

3. **Axios Behavior**: By creating a clean axios instance without default headers and using `transformRequest: [(data) => data]`, we prevent axios from automatically adding headers.

### Files Modified

1. `biormika-new-frontend/src/services/multipartUpload.service.ts`
   - Modified `uploadChunkWithRetry` method to use custom axios instance
   - Removed automatic Content-Type header setting

2. `Biormika-AWS-Backend/storage/multipart_upload.py`
   - Updated comments in `generate_part_upload_urls` function

## Testing Instructions

### 1. Manual Testing

1. Start the local development environment
2. Navigate to the file upload page
3. Select a large file (>10MB to trigger multipart upload)
4. Monitor the browser console for any SignatureDoesNotMatch errors
5. Verify that the upload completes successfully

### 2. Browser Console Testing

A test script has been provided at `test-multipart-upload.js`. To use it:

1. Open browser developer tools
2. Load the test script: `<script src="./test-multipart-upload.js"></script>`
3. Run: `testMultipartUpload()`
4. Check console output for header analysis

### 3. Network Tab Verification

1. Open browser developer tools → Network tab
2. Start a file upload
3. Look for PUT requests to S3 URLs
4. Verify that these requests do NOT include Content-Type headers
5. Confirm that responses return 200 status codes

## Expected Behavior After Fix

1. ✅ Multipart uploads should complete without SignatureDoesNotMatch errors
2. ✅ Large files should upload successfully in chunks
3. ✅ Upload progress should be reported correctly
4. ✅ ETags should be received and processed properly
5. ✅ Retry logic should work for transient network errors

## Monitoring and Debugging

### Console Logs to Watch For

- ✅ `Part X upload completed successfully`
- ❌ `SignatureDoesNotMatch` errors should no longer appear
- ✅ `Multipart upload completed successfully`

### Network Requests to Verify

- PUT requests to S3 should have minimal headers
- No `Content-Type: application/x-www-form-urlencoded` in S3 requests
- Response status should be 200 for successful part uploads

## Rollback Plan

If issues occur, the fix can be quickly reverted by:

1. Restoring the original axios.put() call in `uploadChunkWithRetry`
2. The backend changes are only comments and don't affect functionality

## Additional Notes

- This fix aligns with AWS S3 best practices for multipart uploads
- The solution is compatible with all supported browsers
- No changes to the API contract or data flow
- Existing error handling and retry logic remain intact
